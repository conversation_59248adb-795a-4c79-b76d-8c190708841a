---
type: "always_apply"
---

我将会给你一些功能描述，你生成一份**静态 HTML 文件**（使用 `template.html` 的相同样式）：

### 样式与主题

1. **整体主题色**：`#31969A`
2. **配色方案**：

   * 主色：`#31969A`
   * 辅助色：`#80CCE3`、`#8199C7`、`#E5CE66`
3. **页面要求**：

   * 无副标题（导航栏不显示标题）
   * 使用简洁、清晰的管理可视化风格

### 表格要求

1. 表格数据不少于 **5 条**，内容仿照真实数据。
2. 表格需包含所有功能
3. 表格必须有**完整边框**（上下左右及内部线），并在表格底部添加**分页信息**（如 “共 5 条，每页显示 5 条，第 1 页/共 1 页”）。

### 数据要求
数据内容仿照真实数据

### 数据样例

* **供电公司名称**：

  1. 呼和浩特供电公司（赛罕区）
  2. 包头供电公司（九原区）
  3. 鄂尔多斯供电公司（地址：内蒙古自治区鄂尔多斯市康巴什区团结南路3号）
  4. 乌兰察布供电公司（地址：内蒙古自治区乌兰察布市集宁区建国三路巧味炒面馆旁）
* **管理单位**：从“薛家湾供电公司”中选取
* **台区名称**：仿照内蒙古台区命名，例如“赛罕一台区”、“九原二台区”等

### 输出

生成完整的、可直接打开的 HTML 文件,符合模板样式与上述数据要求，
